/* To learn more about Typescript configuration file: https://www.typescriptlang.org/docs/handbook/tsconfig-json.html. */
/* To learn more about Angular compiler options: https://angular.dev/reference/configs/angular-compiler-options. */
{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "outDir": "../../out-tsc/lib",
    "declaration": true,
    "declarationMap": true,
    "inlineSources": true,
    "types": [],
    "paths": {
      "@snf/design-system-components/*": ["../../node_modules/@snf/design-system-components/dist/*"]
    }
  },
  "exclude": ["**/*.spec.ts"]
}
