import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IconStyles } from '@snf/design-system-components/src/components/icon/icon.component';
import { IconButtonSize } from '@snf/design-system-components/src/components/buttons/icon-button/icon-button.component';

@Component({
  selector: 'snf-library-icon-button',
  templateUrl: './icon-button.component.html',
  styles: [
    `
      :host {
        display: block;
      }
    `,
  ],
  standalone: false,
})
export class IconButtonComponent {
  @Input() public iconStyle: IconStyles = 'filled';
  @Input() public size: IconButtonSize = 'small';
  @Input() public disabled: boolean = false;

  @Output() public iconClick = new EventEmitter<void>();

  public handleClick(event: Event): void {
    event.stopPropagation();
    this.iconClick.emit();
  }
}
