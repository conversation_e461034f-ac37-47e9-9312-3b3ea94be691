import { Component, EventEmitter, Input, Output } from '@angular/core';
import {
  FrameColor,
  ToggleType,
} from '@snf/design-system-components/src/components/verification-frame/verification-frame.component';
import { ToggleValue } from '@snf/design-system-components/src/internals/utils/toggle-value';

@Component({
  selector: 'snf-library-verification-frame',
  templateUrl: './verification-frame.component.html',
  standalone: false,
})
export class VerificationFrameComponent {
  @Input() public frameColor: FrameColor = 'none';
  @Input() public toggleLabelOn: string = 'On';
  @Input() public toggleLabelOff: string = 'Off';
  @Input() public toggleValue: ToggleValue = 'none';
  @Input() public toggleType: ToggleType = 'none';
  @Input() public isToggleDisabled: boolean = false;

  @Output() public toggleOn = new EventEmitter<boolean>();

  public handleToggleOn(event: Event): void {
    event.stopPropagation();
    const customEvent = event as CustomEvent<{ value: boolean }>;
    this.toggleOn.emit(customEvent.detail?.value ?? false);
  }
}
