@use "@styles/style" as snf;
@use "avatar-theme" as theme;

:host {
  .avatar {
    display: block;
    text-align: center;
    align-content: center;
    border: 1px solid snf.color-scheme(on-primary);
    border-radius: 50%;
    height: 32px;
    width: 32px;
    overflow: hidden;
    @include snf.label-medium();
  }

  .avatar-secondary-container {
    @include theme.avatar-color(secondary-container);
  }

  .avatar-primary {
    @include theme.avatar-color(primary);
  }
}
