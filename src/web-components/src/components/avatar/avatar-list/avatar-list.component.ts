import BaseElement from "../../../internals/baseElement/baseElement";
import { html, unsafeCSS } from "lit";
import styles from "./avatar-list.scss?inline";
import registerElement from "../../registerElement";

export default class AvatarList extends BaseElement {
  static override styles = [BaseElement.globalStyles, unsafeCSS(styles)];

  protected override render(): unknown {
    return html`<slot class="avatar-item"></slot>`;
  }
}

registerElement("snf-avatar-list", AvatarList);

declare global {
  interface HTMLElementTagNameMap {
    "snf-avatar-list": AvatarList;
  }
}
