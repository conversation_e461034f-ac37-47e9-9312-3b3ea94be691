import { html, unsafeCSS } from "lit";
import { property } from "lit/decorators.js";
import BaseElement from "../../../internals/baseElement/baseElement";
import styles from "./icon-button.scss?inline";
import { booleanAttributeConverter } from "../../../internals/utils/boolean-attribute-converter";
import registerElement from "../../registerElement";
import "../../icon/icon.component";
import { IconStyles } from "../../icon/icon.component";

export const iconButtonSize = ["small", "medium", "large"] as const;
export type IconButtonSize = (typeof iconButtonSize)[number];


export default class IconButtonComponent extends BaseElement {
  static override styles = [BaseElement.globalStyles, unsafeCSS(styles)];

  @property({ type: String, reflect: true })
  public iconStyle: IconStyles = "filled";

  @property({ type: String, reflect: true })
  public size: IconButtonSize = "small";

  @property({
    type: Boolean,
    reflect: true,
    converter: booleanAttributeConverter,
  })
  public disabled: boolean = false;

  private handleClick(event: Event) {
    if (this.disabled) {
      event.preventDefault();
      event.stopImmediatePropagation();
    } else {
      this.dispatchEvent(new CustomEvent('iconClick', {
        bubbles: true,
        composed: true
      }));
    }
  }

  private handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      this.handleClick(event);
    }
  }

  private getIconSize(size: IconButtonSize) {
    switch (size) {
      case 'small':
        return 'small';
      case 'medium':
        return 'small';
      case 'large':
        return 'medium';
      default:
        return 'small';
    }
  }

  public override render() {
    return html`
      <button
        class="icon-button ${this.size}"
        ?disabled=${this.disabled}
        aria-disabled=${this.disabled}
        @click="${this.handleClick}"
        @keydown="${this.handleKeyDown}"
      >
        <snf-icon
          iconStyle=${this.iconStyle}
          iconSize=${this.getIconSize(this.size)}
        >
          <slot></slot>
        </snf-icon>
      </button>
    `;
  }
}

registerElement("snf-icon-button", IconButtonComponent);

declare global {
  interface HTMLElementTagNameMap {
    "snf-icon-button": IconButtonComponent;
  }
}
